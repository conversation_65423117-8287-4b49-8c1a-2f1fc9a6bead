import { useState, useEffect, useCallback } from 'react';
import { Form, message } from 'antd';
import dayjs from 'dayjs';

// 导入重构后的模块
import type { TaskBasic, TaskAlert, DBConnection, AlertSend, OtherInfo } from '../types';
import { DEFAULT_RETRY_NUM, DEFAULT_FREQUENCY, DEFAULT_RETRY_FREQUENCY } from '../constants';
import { TaskService } from '../services';
import { MonitorItemService } from '../services/monitorItem';
import { AlertSendService } from '../services/alertSend';
import { DBConnectionService } from '../services/dbConnection';

interface UseFormDataProps {
  initialData?: TaskBasic;
}

interface UseFormDataReturn {
  form: any;
  isEditMode: boolean;
  alerts: TaskAlert[];
  alertSends: AlertSend[];
  dbConnection: DBConnection | null;
  otherInfo: OtherInfo | null;
  setAlerts: (alerts: TaskAlert[]) => void;
  setAlertSends: (alertSends: AlertSend[]) => void;
  setDbConnection: (dbConnection: DBConnection | null) => void;
  setOtherInfo: (otherInfo: OtherInfo | null) => void;
  resetFormData: () => void;
  loadRelatedData: () => Promise<void>;
}

/**
 * 表单数据管理 Hook
 * 管理表单状态和相关数据
 */
export const useFormData = ({ initialData }: UseFormDataProps): UseFormDataReturn => {
  const [form] = Form.useForm();
  const [isEditMode, setEditMode] = useState(false);

  // 各种数据状态
  const [alerts, setAlerts] = useState<TaskAlert[]>([]);
  const [alertSends, setAlertSends] = useState<AlertSend[]>([]);
  const [dbConnection, setDbConnection] = useState<DBConnection | null>(null);
  const [otherInfo, setOtherInfo] = useState<OtherInfo | null>(null);

  // 加载关联数据
  const loadRelatedData = useCallback(async () => {
    if (!initialData) return;

    try {
      // 加载告警数据
      if (initialData.alert_task_id && initialData.alert_task_id.length > 0) {
        const alertData = await MonitorItemService.getAlertsByIds(initialData.alert_task_id);
        setAlerts(alertData.data);
      }

      // 加载告警发送数据
      if (initialData.alert_send_id && initialData.alert_send_id.length > 0) {
        const alertSendData = await AlertSendService.getAlertSendsByIds(initialData.alert_send_id);
        setAlertSends(alertSendData.data);
      }

      // 加载数据库连接数据
      if (initialData.db_connection_id) {
        const dbData = await DBConnectionService.getDbConnectionByIds([initialData.db_connection_id]);
        setDbConnection(dbData.data.length > 0 ? dbData.data[0] : null);
      }

      // 加载其他信息数据
      if (initialData.other_info_id) {
        const otherData = await TaskService.getOtherInfoById(initialData.other_info_id);
        setOtherInfo(otherData);
      }
    } catch (error) {
      console.error('加载关联数据失败:', error);
      message.error('加载关联数据失败');
    }
  }, [initialData]);

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      setEditMode(true);

      // 处理初始数据，转换为表单格式
      const formData = {
        name: initialData.name,
        group_name: initialData.group_name,
        status: initialData.status,
        weekday: initialData.weekday || [],
        frequency_value: initialData.frequency?.value || DEFAULT_FREQUENCY.value,
        frequency_unit: initialData.frequency?.unit || DEFAULT_FREQUENCY.unit,
        retry_frequency_value: initialData.retry_frequency?.value || DEFAULT_RETRY_FREQUENCY.value,
        retry_frequency_unit: initialData.retry_frequency?.unit || DEFAULT_RETRY_FREQUENCY.unit,
        retry_num: initialData.retry_num,
        start_time: initialData.start_time ? dayjs(initialData.start_time, 'HH:mm:ss') : undefined,
        end_time: initialData.end_time ? dayjs(initialData.end_time, 'HH:mm:ss') : undefined,
      };
      form.setFieldsValue(formData);

      // 加载关联数据
      loadRelatedData();
    } else {
      setEditMode(false);
      // 设置默认值
      form.setFieldsValue({
        status: 'enabled',
        frequency_value: DEFAULT_FREQUENCY.value,
        frequency_unit: DEFAULT_FREQUENCY.unit,
        retry_frequency_value: DEFAULT_RETRY_FREQUENCY.value,
        retry_frequency_unit: DEFAULT_RETRY_FREQUENCY.unit,
        retry_num: DEFAULT_RETRY_NUM,
      });
    }
  }, [initialData, form, loadRelatedData]);

  // 重置表单数据
  const resetFormData = () => {
    form.resetFields();
    setAlerts([]);
    setAlertSends([]);
    setDbConnection(null);
    setOtherInfo(null);
  };

  return {
    form,
    isEditMode,
    alerts,
    alertSends,
    dbConnection,
    otherInfo,
    setAlerts,
    setAlertSends,
    setDbConnection,
    setOtherInfo,
    resetFormData,
    loadRelatedData,
  };
};
