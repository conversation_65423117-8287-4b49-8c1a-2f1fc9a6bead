/* 自定义表格样式 */
.custom-table .ant-table {
  border-radius: 12px;
  overflow: hidden;
}

.custom-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  color: #374151;
  padding: 16px 12px;
}

.custom-table .ant-table-tbody > tr {
  transition: all 0.2s ease;
}

.custom-table .ant-table-tbody > tr:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.custom-table .ant-table-tbody > tr > td {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
}

.custom-table .ant-table-row-selected {
  background-color: #eff6ff !important;
}

.custom-table .ant-table-row-selected:hover {
  background-color: #dbeafe !important;
}

/* 自定义分页样式 */
.custom-pagination .ant-pagination-item {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.custom-pagination .ant-pagination-item:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.custom-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
}

.custom-pagination .ant-pagination-item-active a {
  color: white;
}

.custom-pagination .ant-pagination-prev,
.custom-pagination .ant-pagination-next {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.custom-pagination .ant-pagination-prev:hover,
.custom-pagination .ant-pagination-next:hover {
  border-color: #3b82f6;
  transform: translateY(-1px);
}

/* 表格容器高度优化 */
.table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

.pagination-wrapper {
  flex-shrink: 0;
  height: 48px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  align-items: center;
}

/* 复杂样式使用小驼峰命名 */
.mainContainer {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: none;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.buttonPrimary {
  background-color: #e0f2fe;
  border-color: #b3e5fc;
  color: #00529b;
  border-radius: 0.375rem;
  flex: 1;
}

.buttonPrimary:hover {
  background-color: #f0f9ff;
  border-color: #e0f2fe;
  color: #00529b;
}

.pulseDot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #3b82f6;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.editButton {
  color: #2563eb;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  transition: all 0.2s;
}

.editButton:hover {
  color: #1d4ed8;
  background-color: #eff6ff;
}

.deleteButton {
  color: #dc2626;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  transition: all 0.2s;
}

.deleteButton:hover {
  color: #b91c1c;
  background-color: #fef2f2;
}

.modalButtonPrimary {
  background-color: #2563eb;
  border-color: #2563eb;
  border-radius: 0.375rem;
  padding: 0.5rem 1.5rem;
}

.modalButtonPrimary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.dataStats {
  display: flex;
  align-items: center;
  color: #6b7280;
  font-weight: 500;
  height: 100%;
}

.dataStats::before {
  content: '';
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  margin-right: 8px;
  animation: pulse 2s infinite;
}

/* 搜索区域样式 */
.searchSection {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

.searchSection .ant-row {
  height: 100%;
  align-items: center;
}

.searchSection .ant-col {
  display: flex;
  align-items: center;
}

.searchSection .ant-form {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.searchSection:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

/* 批量操作区域增强 */
.batchOperations {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 6px 16px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  height: 32px;
  display: flex;
  align-items: center;
}

.batchOperations:hover {
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
}

/* 新增任务按钮增强 */
.addTaskBtn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.addTaskBtn:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  transform: translateY(-2px);
}

/* 分组管理按钮样式 */
.groupManageBtn {
  background: linear-gradient(135deg, #92ea2c 0%, #787765 100%);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
}

.groupManageBtn:hover {
  background: linear-gradient(135deg, #a9acb1 0%, #555a69 100%) !important;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  transform: translateY(-2px);
}

/* 批量操作按钮样式 */
.batchDeleteBtn {
  color: #dc2626;
  border-radius: 6px;
  padding: 4px 12px;
  transition: all 0.2s ease;
}

.batchDeleteBtn:hover {
  background-color: #fef2f2;
  color: #b91c1c;
}

.batchCancelBtn {
  color: #6b7280;
  border-radius: 6px;
  padding: 4px 12px;
  transition: all 0.2s ease;
}

.batchCancelBtn:hover {
  background-color: #f9fafb;
  color: #374151;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* AlertConfigTab 专用表格样式 */
.custom-alert-table {
  border-radius: 0;
}

.custom-alert-table .ant-table {
  border-radius: 0;
  border: none;
}

.custom-alert-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  color: #374151;
  padding: 16px 12px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.custom-alert-table .ant-table-tbody > tr {
  transition: all 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
}

.custom-alert-table .ant-table-tbody > tr:hover {
  background-color: #f0f9ff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.custom-alert-table .ant-table-tbody > tr > td {
  padding: 14px 12px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: middle;
}

/* 监控项级别颜色优化 */
.custom-alert-table .severity-critical {
  color: #dc2626;
  font-weight: 600;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

.custom-alert-table .severity-high {
  color: #ea580c;
  font-weight: 600;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

.custom-alert-table .severity-medium {
  color: #d97706;
  font-weight: 600;
  background: linear-gradient(135deg, #fffbeb 0%, #fde68a 100%);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

.custom-alert-table .severity-low {
  color: #16a34a;
  font-weight: 600;
  background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

/* SQL代码块样式优化 */
.custom-alert-table .sql-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 6px 10px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  color: #475569;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 滚动条样式 */
.ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
